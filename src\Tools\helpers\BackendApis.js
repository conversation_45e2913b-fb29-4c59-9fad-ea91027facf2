import { PostRequestWithHeaders } from "./api"
export default function EndSessionApi(roomId) {
    PostRequestWithHeaders({
        url: process.env.REACT_APP_API_BACKEND_API_URL + 'session/EndSession', body: {
            "session_id": roomId,
        }
    }).then(response => {

    })

}

export async function ExtendSessionApi(sessionId, duration) {
    try {
        const response = await PostRequestWithHeaders({
            url: process.env.REACT_APP_API_BACKEND_API_URL +`session/ExtendSession`,
            body: {
                session_id: sessionId,
                duration: duration
            }
        });
        console.log("Extend session response:", response);
        return response;
    } catch (error) {
        console.error("Error in ExtendSessionApi:", error);
        throw error;
    }
}

export async function CheckAvailabilityApi(config) {
    try {
        const response = await PostRequestWithHeaders({
            url: process.env.REACT_APP_API_BACKEND_API_URL +`session/checkAvailability`,
            body: config
        });
        console.log("Check availability response:", response);
        return response;
    } catch (error) {
        console.error("Error in CheckAvailabilityApi:", error);
        throw error;
    }
}