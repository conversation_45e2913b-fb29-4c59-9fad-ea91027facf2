import React, { Component, lazy, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from "react-redux";
import 'bootstrap/dist/css/bootstrap.min.css';
import Toast from "./components/Toast";
import "./assets/css/material-kit.css?v=2.0.7";
import "./assets/demo/demo.css";
import "../src/assets/css/Custom.css";
import Store from "./Store/Store";

// Lazy load components
const WrapperIndex = lazy(() => import('./Templates/WT/Host/WrapperIndex'));
const WrapperIndexGuest = lazy(() => import('./Templates/WT/Guest/Wrapperindex'));
const WrapperEndSession = lazy(() => import('./components/WraperEndSession'));
const WrapperPreview = lazy(() => import('./Templates/WT/Host/WrapperPreview'));
const WrapperJoinroom = lazy(() => import('./Templates/WT/Guest/WrapperJoinroom'));
const AleGuest = lazy(() => import('./Templates/Ale/Guest/Scene'));

class App extends Component {

  componentDidMount() {
    // const port = process.env.REACT_APP_API_PORT || 5001;
    window.scrollTo(0, 0);
    window.log = function (message) {
      if (process.env.NODE_ENV === "development") {
        var logDiv = document.getElementById('log');
        logDiv.style.display = "block";
        var logMessage = document.createElement('p');
        logMessage.textContent = message;
        logDiv.appendChild(logMessage);
      }
    }
  }

  render() {
    return (
      <>
        <Provider store={Store}>
          <Toast />
          <Router>
            <Suspense fallback={ <div style={{ width: "100%", height: "100%" }}>
            <div
              style={{
                margin: "auto",
                width: 50,
                position: "relative",
                height: 50,
                top: "calc(50% - 80px)"
              }}
            >
              <div className="lds-ring">
                <div />
                <div />
                <div />
                <div />
              </div>
            </div>
          </div>}>
              <Routes>
                <Route path='/salestool/guest/room/:roomid' element={<WrapperIndexGuest/>} exact  />
                <Route path='/salestool/room/:roomid' element={<WrapperIndex/>} exact/>
                <Route path='/salestool/joinroom/:rid' element={<WrapperJoinroom/>} exact/>
                <Route path='/salestool/preview/:roomid' element={<WrapperPreview/>} exact  />
                <Route path='/salestool/feedback/:rid/:key' element={<WrapperEndSession/>} />
                <Route path="/salestool/guest/Aleview/:rid" element={<AleGuest/>} />
              </Routes>
            </Suspense>
          </Router>
        </Provider>
      </>
    )
  }
}

export default App;
