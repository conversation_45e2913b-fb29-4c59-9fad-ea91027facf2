import React from 'react';
import {connect} from "react-redux";
import Wave from '../../../Tools/ToolComponents/AudioVis';
import * as HostActions from "../../../Actions/HostAction"
class VideoCard extends React.Component {
  constructor(props){
    super(props);
    this.ref= React.createRef();
  }
  componentDidMount()
{
  let wave = new Wave();

  const peer = this.props.peer;


    peer.on('stream', (stream) => {
      this.props.ConnectMUX(stream)
      this.ref.current.srcObject = stream;
      wave.fromStream(stream, peer.peerID, {
        colors: ['#ffffff00', '#2979FF'],
      });
    });
    peer.on('track', (track, stream) => {
    });




}
shouldComponentUpdate(nextProps, nextState){
  return true
}
// let wave = new Wave();
  render(){
    return (
      <>
        <div className={" custom-aspect-ratio aspect-video "+ (this.props.userVideoAudio[this.props.peer.peerID]?this.props.userVideoAudio[this.props.peer.peerID].screen?"ScreenSharing fixed-video ":"fixed-video":"fixed-video")} >

      <div className="VideoControls_Div" >
            <div className="VideoOffname" style={{marginRight:"8px",textAlign:"end"}}><div style={{position:"absolute",width:"100%",bottom:"0px"}}> {this.props.peer.userName}</div></div>

          </div>
          {this.props.userVideoAudio[this.props.peer.peerID]
          ?this.props.userVideoAudio[this.props.peer.peerID].audio?
          <button onClick={()=>{this.props.MuteGuest(this.props.peer.extra.id,this.props.userVideoAudio[this.props.peer.peerID].audio)}} className="left-1 top-1 p-1 absolute z-10 bg-[black]/20 bg-opacity-20 rounded-full text-white"><svg  className="w-5 h-5" viewBox="0 0 24 24">
          <path fill="#fff" fillRule="evenodd" d="M13 17.92V20h2.105c.493 0 .895.402.895.895v.21c0 .493-.402.895-.895.895h-6.21C8.402 22 8 21.598 8 21.106v-.211c0-.493.402-.895.895-.895H11v-2.08c-3.387-.488-6-3.4-6-6.92 0-.552.447-1 1-1 .553 0 1 .448 1 1 0 2.757 2.243 5 5 5s5-2.243 5-5c0-.552.447-1 1-1 .553 0 1 .448 1 1 0 3.52-2.613 6.432-6 6.92zM10 6c0-1.103.897-2 2-2s2 .897 2 2v5c0 1.103-.897 2-2 2s-2-.897-2-2V6zm2 9c2.206 0 4-1.794 4-4V6c0-2.205-1.794-4-4-4S8 3.795 8 6v5c0 2.206 1.794 4 4 4z" />
        </svg></button>
        :<button onClick={()=>{this.props.MuteGuest(this.props.peer.extra.id,this.props.userVideoAudio[this.props.peer.peerID].audio)}} className="left-1 top-1 p-1 absolute z-10 bg-[black]/20 bg-opacity-20 rounded-full text-white"><svg className="w-5 h-5"  viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                    <g data-name="mic-off">
                    <rect width="24" height="24" opacity="0"/>
                    <path fill="#fff" d="M10 6a2 2 0 0 1 4 0v5a1 1 0 0 1 0 .16l1.6 1.59A4 4 0 0 0 16 11V6a4 4 0 0 0-7.92-.75L10 7.17z"/>
                    <path fill="#fff" d="M19 11a1 1 0 0 0-2 0 4.86 4.86 0 0 1-.69 2.48L17.78 15A7 7 0 0 0 19 11z"/>
                    <path fill="#fff" d="M12 15h.16L8 10.83V11a4 4 0 0 0 4 4z"/>
                    <path fill="#fff" d="M20.71 19.29l-16-16a1 1 0 0 0-1.42 1.42l16 16a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/>
                    <path fill="#fff" d="M15 20h-2v-2.08a7 7 0 0 0 1.65-.44l-1.6-1.6A4.57 4.57 0 0 1 12 16a5 5 0 0 1-5-5 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H9a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2z"/>
                    </g>
                    </g>
                    </svg></button>  :""}
                    <div >
          <video
      className="user-video"
      id={this.props.peer.peerID+"VIDEO"}
        playsInline
        autoPlay
        ref={this.ref}
      ></video></div>
      </div>
      </>
    );
  }


};



const mapStateTothisprops = state => {
  return {
    userVideoAudio:state.Call.userVideoAudio,
  }
}
const mapActionstothisprops={...HostActions}


export default connect(mapStateTothisprops, mapActionstothisprops)(VideoCard)
