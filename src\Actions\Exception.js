export function SetStickyException(message){
    return dispatch=>{
        dispatch({type:"SET_STICKY_EXCEPTION",message:message})
    }
}
export function SetModalException(message){
    return dispatch=>{
        dispatch({type:"SET_MODAL_EXCEPTION",message:message})
    }
}
export function CreateToast({message,postmessage}){
    return dispatch=>{
        const time = new Date().getTime();
        dispatch({type:"SET_TOAST_TOAST",toast:{[time]:{message,type:"info",postmessage}}})            
        setTimeout(() => {
            dispatch({type:"DELETE_TOAST",toastkey:time})  
        }, 10000);
    }
}


