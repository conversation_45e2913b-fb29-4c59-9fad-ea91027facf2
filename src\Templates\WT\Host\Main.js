import React from 'react';
import { connect } from 'react-redux';
import '../../../styles/video.css';
import * as HostActions from "../../../Actions/HostAction";
import { socket, peersRef} from "../../../Actions/HostAction"
import SideBar from './SideBar';
import * as ExceptionActions from "../../../Actions/Exception";
import RealTimeControls from "./RealTimeControls"
import TypeSwitch from './TypeSwitch';


class Room extends React.Component {
  constructor(props) {
    super(props);
    this.state={
      platform:window.vuplex?false:true,
      fullscreen:false,
      time: new Date()
    }
    this.Sidenav = React.createRef();
    this.PinVideo = React.createRef()
    this.findPeer = this.findPeer.bind(this);
    this.SendDatabyChannel=this.SendDatabyChannel.bind(this);
    this.DataChannelMessage=this.DataChannelMessage.bind(this)
    this.props.ConnecToWebSocket(this.props.roomId, "Host")
    this.props.SetVideoDevices();
    const Avatarsrc=["https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar1.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar2.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar3.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar4.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar5.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar6.png",
]
    const extra = {color:Math.floor(Math.random()*16777215).toString(16), username: (this.props.Auth.UserName?this.props.Auth.UserName:this.props.Auth.DisplayName)+"(Host)", type: "host", id: socket.id,Avatar:Avatarsrc[Math.floor(Math.random() * 5)] }
//
    socket.on('FE-user-leave', ({ userId, userName }) => {
      const peerIdx = this.findPeer(userId);
      if(peerIdx){
        peerIdx.peer.destroy();

        this.props.UpdatePeer(this.props.Peers, userId)
      }

    });
    socket.emit('BE-join-room', { roomId:this.props.roomId, userName:(this.props.Auth.first_name || this.props.Auth.email )+" (Host)", extra: JSON.stringify(extra), audio: this.props.Audio, video: this.props.Video, screen: false });
//
  }

  findPeer(id) {
    return peersRef.find((p) => p.peerID === id);
  }
  componentDidMount() {
    
    setInterval(() => {
      this.setState({
        time:new Date()
      })
    }, 1000);
    document.getElementById("LocalVideo").srcObject = this.props.LocalStream;

    this.props.Call(this.props.LocalStream,this.DataChannelMessage)

  }
  DataChannelMessage(Peer,data){
    let string = new TextDecoder().decode(data);
            let Data=JSON.parse(string)
            if(Data.type==="TRANSCRIPTION"){
              this.props.AddLocalTranscription(Data)

            }




  }

  SendDatabyChannel(Data){
    Object.values(this.props.Peers).map(Peer=>{
      if(Peer && Peer._channel){
         if(Peer._channel.readyState=="open")
      Peer.send(JSON.stringify(Data))
      }

    })
}
 toHumanReadableTime(isoString) {
  const date = new Date(isoString);

  const formattedDate = `${date.toLocaleTimeString()} on ${date.toDateString()}`;

  return formattedDate;
}

  render() {


    return (

      <>
        <div style={{ position: "relative", flex: "auto", height: "100%",  display: "flex", flexDirection: "column" }} className="MainContentDiv" id="MainContentDiv">
          <div style={{ width: '100%', display: 'flex', flexDirection: 'row', flex: 'auto',position:"relative",overflow:"hidden",height:"100%" }}   >
            <div className="Propvr_Embed" id="Propvr_Embed" style={{ "position": "relative", height: "100%", flex: "auto" }}>
                      <>
                      <TypeSwitch platform={this.state.platform} SendDatabyChannel={this.SendDatabyChannel}  SendCustomMessage={this.props.SendCustomMessage} roomId={this.props.roomId} />
                      </>

            </div>


            <SideBar sendmessage={this.sendmessage} PinVideo={this.PinVideo} socketid={socket.id} roomId={this.props.roomId} Sidenav={this.Sidenav} />

          </div>
          <RealTimeControls roomId={this.props.roomId} isFullscreen={this.state.fullscreen} ShowSwitchProject={true}/>
            </div>

      </>
    );
  }
};



const mapStateTothisprops = state => {
  return {
    Peers: state.Call.peers,
    LocalStream: state.Call.LocalStream,
    UserName: state.Call.UserName,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    Auth: state.Auth.auth,
    Config: state.Call.config,
    ProjectDetails: state.Sessions.projectDetails,
    SessionDetails: state.Sessions.sessions,
  }
}

const mapDispatchTothisprops = {
  ...HostActions,
  ...ExceptionActions,

}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(Room)