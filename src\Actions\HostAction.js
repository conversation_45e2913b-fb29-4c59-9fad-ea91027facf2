import io from "socket.io-client"
import Peer from 'simple-peer';
import { Firebase } from "../config/Firebase";
export var socket;
export const peersRef=[];
var AudioContext ;
export var MUX ;
export var MUXDEST;
var c = document.createElement("canvas");
  c.width = 1600;  // 16
  c.height = 900;  // 9
var ctx = c.getContext("2d");
ctx.beginPath();
ctx.rect(20, 20, 150, 100);
// ctx.fillStyle = "red";
ctx.fill();
ctx.beginPath();
ctx.rect(40, 40, 150, 100);
ctx.fillStyle = "black";
ctx.fill();
const canvasstreamg = c.captureStream(1)
export const canvastrack = canvasstreamg.getVideoTracks()[0]
canvastrack.enabled = false;
var screenTrackRef;


const IceServers = { iceServers: [{
  'urls': [
    'stun:stun.l.google.com:19302',
    'stun:stun1.l.google.com:19302',
    'stun:stun2.l.google.com:19302',
    'stun:stun3.l.google.com:19302',
    'stun:stun4.l.google.com:19302',
  ]
},
{
  "urls":"turn:34.18.30.39:19303",
  "username":"propvr",
  "credential":"revsmart@123"
},
{
  "urls":"turn:20.197.40.183:19303",
  "username":"propvr",
  "credential":"revsmart@123"
},

] }
export function ADDPEER(peer,config) {
    return dispatch => {
        dispatch({ type: "ADD_PEER", peer: peer })
    }
}
export function UpdateConfig(peer,config){
  return dispatch=>{
    dispatch({type:"UPDATE_CONFIG",peer:peer,config:config})
  }
}
export function INVERT_CONTROLS(controls){
  console.log('INVERT_CONTROLS action called with:', controls);
  return dispatch=>{
    dispatch({type:"INVERT_CONTROLS"})
    console.log('INVERT_CONTROLS action dispatched');
  }
}

export function TOGGLE_CONTROLS(value){
  console.log('TOGGLE_CONTROLS action called with:', value);
  return dispatch=>{
    dispatch({type:"TOGGLE_CONTROLS", value: value})
    console.log('TOGGLE_CONTROLS action dispatched with value:', value);
  }
}
export function UpdatePeer(peers, userid) {
    return dispatch => {


        // Also remove from peersRef
        const peerIndex = peersRef.findIndex(p => p.peerID === userid);
        if (peerIndex !== -1) {
            peersRef.splice(peerIndex, 1);
            console.log(`UpdatePeer: Removed peer ${userid} from peersRef`);
        }

        dispatch({ type: "UPDATE_PEERS", peerid: userid })
    }
}
export function HasCamera(value){
  return dispatch=>{
    dispatch({type:"SET_HAS_CAMERA",value:value})
  }
}
export function SetConfig(config){
  return dispatch=>{
    dispatch({type:"SET_CONFIG",config:config})
  }
}
export function HasMicrophone(value){
  return dispatch=>{
    dispatch({type:"SET_HAS_MICROPHONE",value:value})
  }
}
export function SetVideoDevices() {
    return dispatch => {
        navigator.mediaDevices.enumerateDevices().then((devices) => {
            const filtered = devices.filter((device) => device.kind === 'videoinput');
            dispatch({ type: "SET_VIDEO_DEVICES", Devices: filtered })
        });
    }
}

export function SetHostStatus(status){
  return dispatch=>{
    dispatch({type:"SET_HOST_STATUS",status:status})
  }
}
export function SetRecorinngsQuota(UID){
  return dispatch=>{
  Firebase.storage().ref().child(UID + "/Salestool/Recordings").listAll().then(Records => {
    if (Records.prefixes.length) {
      Promise.all([...Records.prefixes].map(getRooms)).then(final => {
        dispatch({type:"SET_RECORD_QUOTA",size:final.reduce(function (a, b) { return a + b; }, 0)})
      })
    } else {
      dispatch({type:"SET_RECORD_QUOTA",size:0})
    }
  })


  }
}
export function ConnecToWebSocket(RoomId,Username){
    return dispatch=>{
      socket=io("https://propvr-socket.propvr.tech");
      socket.io.on("error", (error) => {
        // ...
        console.log(error)
      });
        const roomName = RoomId;
        const userName = Username;
        socket.on('FE-error-user-exist', ({ error }) => {
            if (!error) {
              sessionStorage.setItem('user', userName);
              dispatch({type:"CONNECTED_GOOD_TO_GO",socketid:socket.id,userName:Username})
            } else {
              dispatch({type:"CANT_CONNECT",payload:"User name already exist"})
            }
          });
    if (!roomName || !userName) {
      dispatch({type:"ENTER_VALID_FEILDS"})
    } else {
      socket.emit('BE-check-user', { roomId: roomName, userName });
    }
    }
}

export function SetLocalStream(DummyAudio){
    return dispatch=>{



      GetUserVideoMedia(true).then(Videotrack => {
        let RenderStream;
        if (Videotrack.stream) {

          dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:true})
          dispatch({type:"SET_HAS_CAMERA",value:true})
          RenderStream = Videotrack.stream;
        } else {

          if ((Videotrack.error.toString()).includes("denied")) {
            dispatch({type:"SET_MODAL_EXCEPTION",message:"Camera Access is not allowed"})
          } else if ((Videotrack.error.toString()).includes("device")) {
            dispatch({type:"SET_MODAL_EXCEPTION",message:"Can't find any Camera attached to browser"})
          }
          dispatch({type:"SET_HAS_CAMERA",value:false})

          dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:false})

          RenderStream = new MediaStream([canvastrack]);

        }
        return GetUserAudioMedia({ echoCancellation: true }).then(AudioTrack => {
          if (AudioTrack.stream && AudioTrack.track) {

    dispatch({type:"SET_HAS_MICROPHONE",value:true})

          dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Audio",value:true})

            RenderStream.addTrack(AudioTrack.track);

            return RenderStream;

          } else {

            if ((AudioTrack.error.toString()).includes("denied")) {
        dispatch({type:"SET_MODAL_EXCEPTION",message:"Mic Access is not allowed"})
            } else if ((AudioTrack.error.toString()).includes("device")) {
        dispatch({type:"SET_MODAL_EXCEPTION",message:"Can't find any Mic attached to browser"})
            }
    dispatch({type:"SET_HAS_MICROPHONE",value:false})

            RenderStream.addTrack(DummyAudio)
          dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Audio",value:false})

            return RenderStream

          }
        })
      }).then(FinalStream=>{
        AudioContext=  window.AudioContext // Default
    || window.webkitAudioContext // Safari and old versions of Chrome
    || false;
    MUX = new AudioContext();
    MUXDEST=MUX.createMediaStreamDestination()

    // RESTORE: Connect host's local stream to MUX for audio output
    // The issue was not the connection itself, but how toggles affected it
    var _tempStream= new MediaStream([FinalStream.getAudioTracks()[0]])
    MUX.createMediaStreamSource(_tempStream).connect(MUXDEST);
    console.log('MUX system initialized with host audio - toggles will not affect guest audio');

    dispatch({type:"ADD_LOCAL_STREAM",LocalStream:FinalStream})

      })


    }
}
export function ConnectMUX(stream){
  return dispatch=>{
    dispatch({type:"MUX"})
    var _tempStreamremote= new MediaStream([stream.getAudioTracks()[0]])
    MUX.createMediaStreamSource(_tempStreamremote).connect(MUXDEST);
  }
}
export function MuteAll(roomId,status){
  return dispatch=>{
    socket.emit('HostMuteAll', { roomId:roomId, status:status });
    dispatch({type:""});
  }
}
export function Mute(id,roomId,status){
  return dispatch=>{
    socket.emit('HostMute', { id:id,roomId:roomId, status:status });
    dispatch({type:""});
  }
}
export function CheckInternetSpeed(){
  return dispatch=>{
    const SetNetwork=(network=>{
      if(network.downlink<= 1){
                    var time = new Date().getTime();

                    dispatch({type:"NETWORK_SPEED",payload:{speed:network.downlink,strength:"POOR"}})
                    // dispatch({type:"SET_TOAST_TOAST",toast:{[time]:{message:"Your network connectivity is unstable",type:"info",postmessage:""}}})
                    setTimeout(() => {
                      dispatch({type:"DELETE_TOAST",toastkey:time})
                    }, 5000);
                  }
                  else if(network.downlink>1 && network.downlink<=3){

                  // dispatch({type:"SET_TOAST_TOAST",toast:{[time]:{message:"Your network connectivity is unstable",type:"info",postmessage:""}}})
                  setTimeout(() => {
                    dispatch({type:"DELETE_TOAST",toastkey:time})
                  }, 5000);

                    dispatch({type:"NETWORK_SPEED",payload:{speed:network.downlink,strength:"MODERATE"}})

                  }else if(network.downlink>3 && network.downlink<=10){
                    dispatch({type:"NETWORK_SPEED",payload:{speed:network.downlink,strength:"GOOD"}})

                  }else if(network.downlink>=10){
                    dispatch({type:"NETWORK_SPEED",payload:{speed:network.downlink,strength:"GOOD"}})

                  }
    })
  if(navigator.connection!=undefined){
    const connection=navigator.connection;
    SetNetwork(navigator.connection)

    setInterval(() => {
    SetNetwork(navigator.connection)
    }, 90000);
    connection.onchange=(network=>{
     SetNetwork(network.target);
    })
  }
  else{
    testConnectionSpeed.run(1.5, (mbps)=>{
SetNetwork({downlink:mbps})
    }, (mbps)=>{
    } )

  }

  }
}
export function StartRecording(){
  return dispatch=>{

    dispatch({type:"START_RECORDING"})



  }
}
export function StopRecord(){
  return dispatch=>{
    dispatch({type:"STOP_RECORD"})

  }
}

export function SetUserAV(userName,config){
  return dispatch=>{
    dispatch({type:"SET_AV_CONFIG",userName:userName,config:config})
  }
}
export function ToogleLocalAV(type,value){
  return dispatch=>{
        dispatch({type:"TOGGLE_LOCAL_AV",MediaType:type,value:value})
  }
}
export function SetMessage(message){
  return dispatch=>{
    dispatch({type:"SET_MESSAGE",data:message})
  }
}
export function Call(stream,DataChannelMessage){
  return  dispatch=>{
    // setInterval(() => {
    //   document.getElementById("LocalVideo").srcObject = stream;
    // }, 5000);

    socket.on('FE-user-join', (users) => {
      // if(!document.getElementById("LocalVideo").srcObject)
      // all users
      // const peers = [];
      users.forEach(({ userId, info }) => {

        let { userName, video, audio,extra,screen } = info;

        if (userId !== socket.id && !findPeer(userId )) {
            // alert(socket.id)

          // dispatch({type:"CREATE_NEW_PEER",UserID:userId,user})

          const peer = createPeer(userId, socket.id, stream);
          peer.userName = userName;
          peer.peerID = userId;
          peer.extra=extra
          peer.Isdestroyed=false;
          peersRef.push({
            peerID: userId,
            peer,
            userName,
            extra
          });
          // dispatch({type:"CREATE_NEW_PEER",userID:userId,user})
          peer.on('data', data => {
            let string = new TextDecoder().decode(data);
                    let Data=JSON.parse(string)
                    if(Data.type==="TRANSCRIPTION"){
                      this.props.AddLocalTranscription(Data)

                    }

                    if(Data.type==="GUEST_LOOK_AT_TO_HOST"){
                      if(document.getElementById(peer.peerID+"_VIDEOSPHERE"))
                      document.getElementById(peer.peerID+"_VIDEOSPHERE").setAttribute("position",Data.position)
                      // document.getElementById(peer.peerID+"_VIDEOSPHERE").setAttribute("animation","property: position; to: "+Data.position+"; loop: false; dur: 200")

                    }



            })


          dispatch({ type: "ADD_PEER", peer: peer });
          dispatch({type:"SET_AV_CONFIG",userName:userId,config:{ video, audio,screen }})
          let index = 5;
          setInterval(() => {
            if(index){
              const localVideo = document.getElementById("LocalVideo");
              if (localVideo) {
                localVideo.srcObject = stream;
              }
              index--;
            }
          }, 5000);
        }
      });

    });
    socket.on('FE-receive-call', ({ signal, from, info }) => {
      let { userName, video, audio,extra,screen } = info;
      const peerIdx = findPeer(from);
      // if(!document.getElementById("LocalVideo").srcObject)

      if (!peerIdx) {
        const peer = addPeer(signal, from, stream);
        peer.userName = userName;
        peer.peerID = from;
        peer.extra=extra
        peersRef.push({
          peerID: from,
          peer,
          userName: userName,
          extra
        });
        peer.on('data', data => {
          let string = new TextDecoder().decode(data);
                    let Data=JSON.parse(string)
                    if(Data.type==="TRANSCRIPTION"){
                      this.props.AddLocalTranscription(Data)
                    }

                    if(Data.type==="GUEST_LOOK_AT_TO_HOST"){
                      if(document.getElementById(peer.peerID+"_VIDEOSPHERE"))
                      // document.getElementById(peer.peerID+"_VIDEOSPHERE").setAttribute("animation","property: position; to: "+Data.position+"; loop: false; dur: 200")
                      document.getElementById(peer.peerID+"_VIDEOSPHERE").setAttribute("position",Data.position)
                    }


          })

        dispatch({ type: "ADD_PEER", peer: peer });
        dispatch({type:"SET_AV_CONFIG",userName:from,config:{ video, audio,screen }})

        console.log('New peer joined with initial AV state:', {
          from,
          video,
          audio,
          screen,
          peerID: peer.peerID
        });

        // Ensure the peer's initial audio state is properly set
        setTimeout(() => {
          if (peer.streams && peer.streams[0]) {
            const audioTrack = peer.streams[0].getAudioTracks()[0];
            if (audioTrack) {
              console.log(`Setting initial audio state for peer ${from}:`, audio);
              audioTrack.enabled = audio;
            }
          }
        }, 1000);

        let index = 5;
        setInterval(() => {
          if(index){
            const localVideo = document.getElementById("LocalVideo");
            if (localVideo) {
              localVideo.srcObject = stream;
            }
            index--;
          }
        }, 5000);



      }
    });
    socket.on('FE-toggle-camera', ({ userId, switchTarget,value }) => {
      console.log('FE-toggle-camera received:', {
        userId,
        switchTarget,
        value,
        currentSocketId: socket.id,
        peersRefLength: peersRef.length
      });

      const peerIdx = findPeer(userId);
      if(peerIdx) {
        console.log('Found peer for toggle event:', {
          peerID: peerIdx.peerID,
          Target: switchTarget,
          value,
          peerConnected: peerIdx.peer.connected,
          peerDestroyed: peerIdx.peer.destroyed
        });

        dispatch({type:"SET_AV_INIT",peerID:peerIdx.peerID,Target:switchTarget,value:value})

        // CRITICAL FIX: Update the actual audio track state in the peer's stream
        if (switchTarget === 'Audio' && peerIdx.peer && peerIdx.peer.streams && peerIdx.peer.streams[0]) {
          const audioTrack = peerIdx.peer.streams[0].getAudioTracks()[0];
          if (audioTrack) {
            console.log(`Updating peer ${userId} audio track enabled state from ${audioTrack.enabled} to:`, value);
            audioTrack.enabled = value;

            // Force update the audio visualization
            const videoElement = document.getElementById(`remoteVideo-${userId}`);
            if (videoElement && videoElement.srcObject) {
              console.log(`Refreshing video element for peer ${userId}`);
              // Trigger a stream update to refresh audio visualization
              const currentStream = videoElement.srcObject;
              videoElement.srcObject = null;
              setTimeout(() => {
                videoElement.srcObject = currentStream;
              }, 10);
            }
          } else {
            console.log(`No audio track found for peer ${userId}`);
          }
        }
      } else {
        console.log('Peer not found for userId:', userId, 'Available peers:', peersRef.map(p => p.peerID));
      }
   });
   socket.on('FE-call-accepted', ({ signal, answerId }) => {
    const peerIdx = findPeer(answerId);
    peerIdx.peer.signal(signal);
  });

  }

}
export function AddLocalTranscription(Data){
  return dispatch=>{
    dispatch({type:"ADD_TRANSCRIPTION",payload:Data})
    if(Data.isFinal){

      setTimeout(() => {
    dispatch({type:"REMOVE_TRANSCRIPTION",payload:Data})

  },2000);

    }

  }

}
export function SubscribeToChat(OnMessage){
  socket.on('FE-receive-message', ({ msg, sender }) => {
    var data=JSON.parse(msg);
    if(data.actiontype==="chat"){
OnMessage(msg)
setTimeout(() => {
  var element = document.getElementById("chat_bar");
  if(element)
  element.scrollTop = element.scrollHeight;
}, 500);
      }
    });
}
export function SubscribeToCustom(OnMessage){
  return dispatch=>{
    socket.on('FE-receive-custom', ({ msg, sender }) => {
      window.log("Received :"+JSON.stringify(msg,null,2));

    OnMessage(msg)

    });
  }

}
export function SetScreenShare(value){
  return dispatch=>{
    dispatch({type:"SETSCREENSHARE",value:value})
  }
}
export function SetClientData(data){
  return dispatch=>{
    dispatch({type:"SET_CLIENT_DATA",payload:data})
  }
}

export function SetLastUpdate(LastUpdate){
  return dispatch=>{
    dispatch({type:"SET_LAST_UPDATE",LastUpdate:LastUpdate})
  }
}

export function ToggleUserVideo(Config){
  return dispatch=>{
    if(Config.Video){

    Object.values(Config.Peers).forEach(peer=>{
      if(peer){
      peer.replaceTrack(
        peer.streams[0]
          .getTracks()
          .find((track) => track.kind === 'video'),
          canvastrack,
        Config.LocalStream
      );
      }

  })
  Config.LocalStream.removeTrack(Config.LocalStream.getVideoTracks()[0])
  Config.LocalStream.addTrack(canvastrack)
  dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:false})
  dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: false, audio: Config.Audio, screen: Config.Screen }})
  socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Video",value:false });

}
else{


  GetUserVideoMedia(true).then(Videotrack=>{
    if (Videotrack.stream && Videotrack.track) {
      Object.values(Config.Peers).forEach(peer=>{
          if(peer){
             peer.replaceTrack(
          peer.streams[0]
            .getTracks()
            .find((track) => track.kind === 'video'),
            Videotrack.track,
            Config.LocalStream
        );
          }


    })
    Config.LocalStream.removeTrack(Config.LocalStream.getVideoTracks()[0])
    Config.LocalStream.addTrack(Videotrack.track)
    dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Video",value:true})
    dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{ video: true, audio: Config.Audio, screen: Config.Screen }})
    socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Video",value:true });

  } else {
      if ((Videotrack.error.toString()).includes("denied")) {
        dispatch({type:"SET_MODAL_EXCEPTION",message:"Camera Access is not allowed"})
      } else if ((Videotrack.error.toString()).includes("device")) {
        dispatch({type:"SET_MODAL_EXCEPTION",message:"Can't find any Camera attached to browser"})
      }
     }
  })
}
}



}


export function ToggleUserAudio(Config){
  return dispatch=>{
    const userAudioTrack = Config.LocalStream.getAudioTracks()[0];

    if(!userAudioTrack) {
      console.error('No audio track found in LocalStream');
      return;
    }

    console.log('ToggleUserAudio called:', {
      currentAudioState: Config.Audio,
      trackEnabled: userAudioTrack.enabled
    });

    if(Config.Audio){
      // Turning audio OFF
      userAudioTrack.enabled = false;
      dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Audio",value:false})
      dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{  video: Config.Video, audio: false,screen: Config.Screen }})
      socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Audio",value:false });
    }else{
      // Turning audio ON
      userAudioTrack.enabled = true;
      dispatch({type:"TOGGLE_LOCAL_AV",MediaType:"Audio",value:true})
      dispatch({type:"SET_AV_CONFIG",userName:"localUser",config:{  video: Config.Video, audio: true,screen: Config.Screen }})
      socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "Audio",value:true });
    }

    console.log('Audio toggle completed:', {
      newAudioState: !Config.Audio,
      localTrackEnabled: userAudioTrack.enabled
    });
  }
}
export function ToggleScreenVideo(Config){
  return dispatch=>{

    const SetTrack=(track)=>{
      Object.values(Config.Peers).forEach(( peer ) => {
        // replaceTrack (oldTrack, newTrack, oldStream);
        if(peer){
           peer.replaceTrack(
          peer.streams[0]
            .getTracks()
            .find((track) => track.kind === 'video'),
            track,
          Config.LocalStream
        );
        }

      });
      Config.LocalStream.removeTrack(Config.LocalStream.getVideoTracks()[0])
      Config.LocalStream.addTrack(track)
    }
    if(!Config.Screen){
    navigator.mediaDevices
        .getDisplayMedia({ cursor: true })
        .then((stream) => {
          const screenTrack = stream.getTracks()[0];

          SetTrack(screenTrack)
          // Listen click end
          screenTrack.onended = () => {
            if(Config.Video){
              GetUserVideoMedia(Config.Video).then(Videotrack=>{
                if (Videotrack.stream && Videotrack.track) {
                  SetTrack(Videotrack.track)
                   }
                else{
                  SetTrack(canvastrack)
                }
               })
            }
            else{
              SetTrack(canvastrack)
            }
            dispatch({type:"SETSCREENSHARE",value:false})

           socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "screen",value:false });
          };
          socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "screen",value:true });

          Config.LocalStream.removeTrack(Config.LocalStream.getVideoTracks()[0])
        Config.LocalStream.addTrack(stream.getVideoTracks()[0])
          screenTrackRef = screenTrack;
          dispatch({type:"SETSCREENSHARE",value:true})
        });}
        else{
          if(Config.Video){
            GetUserVideoMedia(Config.Video).then(Videotrack=>{
              if (Videotrack.stream && Videotrack.track) {
                SetTrack(Videotrack.track)
                 }
              else{
                SetTrack(canvastrack)
              }
             })
          }
          else{
            SetTrack(canvastrack)
          }
          // screenTrackRef.stop();

          dispatch({type:"SETSCREENSHARE",value:false})

         socket.emit('BE-toggle-camera-audio', { roomId:Config.roomId, switchTarget: "screen",value:false });

        }
  }
}


export function SetScreenShareMinimized(value){
  return dispatch=>{
    dispatch({type:"SET_SCREENSHARE_MINIMIZED",value:value})
  }
}

export function TabControl(value){
  return dispatch=>{
    dispatch({type:"SET_TAB",value:value})
    window.dispatchEvent(new Event('resize'));
    if(value=="CHAT")
    {setTimeout(() => {
      var element = document.getElementById("chat_bar");
      if(element)
      element.scrollTop = element.scrollHeight;
    }, 500);}
  }
}

export function ToggleChat(value){
  return dispatch=>{
    dispatch({type:"TOGGLE_CHAT",value:value})
    window.dispatchEvent(new Event('resize'));
    if(value === true || (value !== false && !value))
    {setTimeout(() => {
      var element = document.getElementById("chat_bar");
      if(element)
      element.scrollTop = element.scrollHeight;
    }, 500);}
  }
}

export function ToggleMembers(value){
  return dispatch=>{
    dispatch({type:"TOGGLE_MEMBERS",value:value})
    window.dispatchEvent(new Event('resize'));
//     setTimeout(() => {
//       var width=window.innerWidth-320;
//       var height=document.getElementById("Propvr_Embed").clientHeight;
//       $('#room').css({'position':'absolute','height':height+"px",'width':width+"px"});
//  // $('#room').attr('embedded',false);
//       let aScene = document.querySelector('a-scene');

//       // Inside the code that fires on resize (you should be able to use the 'onresize' event listener)
//       aScene.camera.aspect =width/height;
//       aScene.camera.updateProjectionMatrix();
//     }, 1000);
setTimeout(() => {
  if(document.querySelector('a-scene')){
    let aScene = document.querySelector('a-scene');

    // Inside the code that fires on resize (you should be able to use the 'onresize' event listener)
    aScene.camera.aspect = aScene.clientWidth/aScene.clientHeight;
    aScene.camera.updateProjectionMatrix();
  }

}, 300);
  }
}


export function findPeer(id) {
  return peersRef.find((p) => p.peerID === id);
}
export function SendMessage(data,roomId) {
  var msg =JSON.stringify({...data,user:socket.id});
      socket.emit('BE-send-message', { roomId:roomId, msg, sender: socket.id });
return dispatch=>{

}
  }
export function SendCustomMessage(data,roomId) {
    var msg =JSON.stringify({...data,user:socket.id});
    window.log("Sent :"+JSON.stringify({ roomId:roomId, msg, sender: socket.id },null,2));

        socket.emit('BE-send-custom', { roomId:roomId, msg, sender: socket.id });
  return dispatch=>{

  }
    }
function createPeer(userId, caller, stream) {
    console.log(`Creating peer connection for user ${userId}`);
    const peer = new Peer({
      initiator: true,
      trickle: false,
      stream,
      config: IceServers
    });

    peer.on('signal', (signal) => {
      console.log(`Sending signal to user ${userId}`);
      socket.emit('BE-call-user', {
        userToCall: userId,
        from: caller,
        signal,
      });
    });

    peer.on('connect', () => {
      console.log(`Peer ${userId} connected successfully`);
      // Sync current media state with the newly connected peer
      const peerObj = findPeer(userId);
      if (peerObj) {
        setTimeout(() => {
          syncMediaStateWithPeer(peerObj, stream);
        }, 1000); // Small delay to ensure connection is stable
      }
    });

    peer.on('stream', (remoteStream) => {
      console.log(`Received stream from peer ${userId}:`, remoteStream);
    });

    peer.on('error', (error) => {
      console.error(`Peer ${userId} error:`, error);
    });

    peer.on('disconnect', () => {
      console.log(`Peer ${userId} disconnected, attempting reconnection`);
      try {
        peer.reconnect();
      } catch (error) {
        console.error(`Failed to reconnect peer ${userId}:`, error);
      }
    });

    peer.on('close', () => {
      console.log(`Peer ${userId} connection closed`);
    });

    return peer;
  }

  function addPeer(incomingSignal, callerId, stream) {
    console.log(`Adding peer connection for caller ${callerId}`);
    const peer = new Peer({
      initiator: false,
      trickle: false,
      stream,
      config: IceServers
    });

    peer.on('signal', (signal) => {
      console.log(`Sending accept signal to caller ${callerId}`);
      socket.emit('BE-accept-call', { signal, to: callerId });
    });

    peer.on('connect', () => {
      console.log(`Peer ${callerId} connected successfully`);
      // Sync current media state with the newly connected peer
      const peerObj = findPeer(callerId);
      if (peerObj) {
        setTimeout(() => {
          syncMediaStateWithPeer(peerObj, stream);
        }, 1000); // Small delay to ensure connection is stable
      }
    });

    peer.on('stream', (remoteStream) => {
      console.log(`Received stream from peer ${callerId}:`, remoteStream);
    });

    peer.on('error', (error) => {
      console.error(`Peer ${callerId} error:`, error);
    });

    peer.on('disconnect', () => {
      console.log(`Peer ${callerId} disconnected`);
      // For incoming peers, we don't automatically reconnect
      // The initiator should handle reconnection
    });

    peer.on('close', () => {
      console.log(`Peer ${callerId} connection closed`);
    });

    peer.signal(incomingSignal);

    return peer;
  }

  export async function CombineStream(stream){
    return new Promise ((resolve,reject)=>{
      const videoTrack = stream.getVideoTracks()[0];
      if(stream.getAudioTracks()>0){
        var _tempStream= new MediaStream([stream.getAudioTracks()[0]])
      MUX.createMediaStreamSource(_tempStream).connect(MUXDEST);
      }

              const mixedTracks = MUXDEST.stream.getTracks()[0];
              const finalstream = new MediaStream([videoTrack, mixedTracks]);
              resolve(finalstream);
    })
  }
export function SetUnreadMessage(count){
  return dispatch=>{
    dispatch({type:"SET_UNREAD_MESSAGE",count:count})
  }
}
export function GetUserVideoMedia(constraint){
  return new Promise((resolve,reject)=>{
    if(constraint){
    navigator.mediaDevices.getUserMedia({video:constraint}).then(stream=>{
resolve({track:stream.getVideoTracks()[0],stream:stream,error:false})
    }).catch(err=>{
    resolve({track:false,stream:false,error:err})
    })
  }else{
    resolve({track:false,stream:false,error:false})
  }
})}

export function GetUserAudioMedia(constraint){
  return new Promise((resolve,reject)=>{
    if(constraint){
    navigator.mediaDevices.getUserMedia({audio:constraint}).then(stream=>{
resolve({track:stream.getAudioTracks()[0],stream:stream,error:false})
    }).catch(err=>{
    resolve({track:false,stream:false,error:err})
    })
  }else{
    resolve({track:false,stream:false,error:false})
  }
})}

var endTime;
var startTime;
  var testConnectionSpeed = {
    imageAddr : "https://upload.wikimedia.org/wikipedia/commons/a/a6/Brandenburger_Tor_abends.jpg", // this is just an example, you rather want an image hosted on your server
    downloadSize : 2707459, // this must match with the image above
    run:function(mbps_max,cb_gt,cb_lt){
      testConnectionSpeed.mbps_max = parseFloat(mbps_max) ? parseFloat(mbps_max) : 0;
      testConnectionSpeed.cb_gt = cb_gt;
      testConnectionSpeed.cb_lt = cb_lt;
      testConnectionSpeed.InitiateSpeedDetection();
    },
    InitiateSpeedDetection: function() {

      window.setTimeout(testConnectionSpeed.MeasureConnectionSpeed, 1);
    },
    result:function(){
      var duration = (endTime - startTime) / 1000;
      var bitsLoaded = testConnectionSpeed.downloadSize * 8;
      var speedBps = (bitsLoaded / duration).toFixed(2);
      var speedKbps = (speedBps / 1024).toFixed(2);
      var speedMbps = (speedKbps / 1024).toFixed(2);
      testConnectionSpeed.cb_gt(speedMbps)

      // if(speedMbps >= (testConnectionSpeed.max_mbps ? testConnectionSpeed.max_mbps : 1) ){
      //   testConnectionSpeed.cb_gt ? testConnectionSpeed.cb_gt(speedMbps) : false;
      // }else {
      //   testConnectionSpeed.cb_lt ? testConnectionSpeed.cb_lt(speedMbps) : false;
      // }
    },
    MeasureConnectionSpeed:function() {
      var download = new Image();
      download.onload = function () {
          endTime = (new Date()).getTime();
          testConnectionSpeed.result();
      }
      startTime = (new Date()).getTime();
      var cacheBuster = "?nnn=" + startTime;
      download.src = testConnectionSpeed.imageAddr + cacheBuster;
    }
  }
 function getProjectsRecords(Projects) {
    return new Promise((resolve, reject) => {

      Projects.child("screen").listAll().then(Files => {
        if (Files.items.length) {

          Promise.all([...Files.items].map(getMetadata)).then(response => {
            resolve(response.reduce(function (a, b) { return a + b; }, 0))

          }
          )

        }
        else {
          resolve(0)
        }

      })

    })
  }
 export function getRooms(folderref) {
    return new Promise((resolve, reject) => {

      folderref.child("rooms").listAll().then((Rooms) => {
        Promise.all([...Rooms.prefixes].map(getProjectsRecords)).then(meta => {
          resolve(meta.reduce(function (a, b) { return a + b; }, 0))
        })

      })
    })
  }
 function getMetadata(File) {

    return new Promise((resolve, reject) => {
      File.getMetadata().then(Metadata => {


        resolve(Metadata.size)

      })


    })
  }

// Function to unsubscribe from chat events
export function UnsubscribeFromChat() {
  if (socket) {
    socket.off('FE-receive-message');
  }
}