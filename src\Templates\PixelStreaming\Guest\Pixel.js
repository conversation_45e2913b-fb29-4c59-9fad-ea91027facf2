import React from "react"
// import SceneControls from "../../../Tools/ToolComponents/SceneControlsGuest";
import { connect } from "react-redux"
import * as HostActions from "../../../Actions/HostAction"
import * as ExceptionActions from "../../../Actions/Exception"
import { socket } from "../../../Actions/HostAction";
import Fire, { Firebase } from "../../../config/Firebase.jsx";

class MP extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            lock: true,
            real_time_config:false,
            hasShownEndingToast: false
        }

    }

    async componentDidMount() {
        Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").onSnapshot({includeMetadataChanges: true},(doc)=>{
            const config_data = doc.data();
            this.setState({
              real_time_config:config_data
            })
            console.log("real_time_config",config_data)
        })
        this.props.SubscribeToCustom((msg) => {
            var data = JSON.parse(msg);
            if (data.targetuser == "All" || data.targetuser == socket.id) {
                if (data.actiontype == "unlock") {
                    this.props.CreateToast({ message: "Now you have control" })
                    this.setState({
                        lock: false
                    })
                }
                if (data.actiontype == "lock") {
                    this.props.CreateToast({ message: "Your control has been revoked by host" })
                    this.setState({
                        lock: true
                    })
                }
            } else {
                this.setState({
                    lock: true
                })
            }

        })
    }
    componentDidUpdate(prevProps) {
      if (this.props.isNearingEnd && !prevProps.isNearingEnd && !this.state.hasShownEndingToast) {
          this.props.CreateToast({
              message: "Session Ending Soon",
              postmessage: "The session will end in less than 5 minutes.",
          });
          this.setState({ hasShownEndingToast: true });
      }
  }
    focusIframe() {
        var iframe = document.getElementById('showcase-iframe');
        if(iframe)
        iframe.contentWindow.focus();

    }
    render() {
        if(this.state.real_time_config && this.state.real_time_config.pixel_streaming_link){
            return (
                <>
                    <iframe
                        onMouseEnter={() => { this.focusIframe() }}
                        onClick={() => { this.focusIframe() }}
                        style={{ width: "100%", height: '100%', position: "absolute", cursor: "pointer" }}
                        src={this.state.real_time_config.pixel_streaming_link}
                        frameborder="0"
                        allow="fullscreen; vr"
                        id="showcase-iframe"></iframe>

                    {this.state.lock ? <><div style={{ position: "absolute", width: "100%", height: "100%", bottom: "0" }}></div>
                        <div style={{ position: "absolute", width: "100%", height: "100%", top: "0" }}></div></> : <></>}

                </>
            )
        }else{
            return (
              <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-50 flex items-center justify-center">
              <div className="bg-black/40 backdrop-blur-md p-8 rounded-xl shadow-lg text-center">
                <div className="lds-ring mb-4">
                  <div></div>
                  <div></div>
                  <div></div>
                  <div></div>
                </div>
                <h2 className="text-xl font-bold text-white mb-2">
                  Host is Joining
                </h2>
                <p className="text-sm text-gray-300">
                  Please wait while your host connects to the session...
                </p>
              </div>
              <style jsx>{`
                .lds-ring {
                  display: inline-block;
                  position: relative;
                  width: 80px;
                  height: 80px;
                }
                .lds-ring div {
                  box-sizing: border-box;
                  display: block;
                  position: absolute;
                  width: 64px;
                  height: 64px;
                  margin: 8px;
                  border: 4px solid #FFFFFF;
                  border-radius: 50%;
                  animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
                  border-color: #000000 transparent transparent transparent;
                }
                .lds-ring div:nth-child(1) {
                  animation-delay: -0.45s;
                }
                .lds-ring div:nth-child(2) {
                  animation-delay: -0.3s;
                }
                .lds-ring div:nth-child(3) {
                  animation-delay: -0.15s;
                }
                @keyframes lds-ring {
                  0% {
                    transform: rotate(0deg);
                  }
                  100% {
                    transform: rotate(360deg);
                  }
                }
              `}</style>
            </div>
            )
        }

    }
}
const mapStateToProps = state => {
    return {
      configDetails: state.Sessions.configData,
    }
  }
const mapDispatchTothisprops = {
    ...HostActions,
    ...ExceptionActions,
}

export default connect(mapStateToProps, mapDispatchTothisprops)(MP)